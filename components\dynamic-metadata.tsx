'use client';

import { useEffect } from 'react';
import { useTranslation } from '@/lib/i18n';

export function DynamicMetadata() {
  const { t } = useTranslation();

  useEffect(() => {
    document.title = t('metadata.title');
    const descriptionTag = document.querySelector('meta[name="description"]');
    if (descriptionTag) {
      descriptionTag.setAttribute('content', t('metadata.description'));
    }
  }, [t]);

  return null;
}
