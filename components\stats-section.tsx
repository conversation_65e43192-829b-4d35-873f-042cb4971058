"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { useTranslation } from "@/lib/i18n"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

// 统计项目的数据结构
// Data structure for individual stat items
interface StatItem {
  name: string  // 项目名称 / Item name
  icon: string  // 表情符号图标 / Emoji icon
}

// 统计卡片的数据结构
// Data structure for stat cards
interface StatCard {
  title: string     // 卡片标题 / Card title
  subtitle: string  // 卡片副标题 / Card subtitle
  items: StatItem[] // 卡片项目列表 / List of card items
}

// 组件属性接口
// Component props interface
interface StatsSectionProps {
  lang?: "zh" | "en"  // 可选的语言参数 / Optional language parameter
}

/**
 * 统计项目滑动组件
 * Slider component for stat items
 * 
 * 功能：
 * - 每次显示最多3个项目
 * - 支持左右滑动切换
 * - 显示分页指示器
 * - 响应式设计适配移动端
 * 
 * Features:
 * - Display up to 3 items at a time
 * - Support left/right slide navigation
 * - Show pagination indicators
 * - Responsive design for mobile
 */
const StatSlider = ({ items }: { items: StatItem[] }) => {
  // 当前显示的页面索引 / Current page index
  const [currentIndex, setCurrentIndex] = useState(0)

  // 下一页处理函数 / Next page handler
  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % Math.ceil(items.length / 3))
  }

  // 上一页处理函数 / Previous page handler
  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + Math.ceil(items.length / 3)) % Math.ceil(items.length / 3))
  }

  // 计算当前页面应该显示的项目 / Calculate visible items for current page
  const visibleItems = items.slice(currentIndex * 3, (currentIndex + 1) * 3)

  return (
    <div className="relative">
      {/* 项目网格布局 / Items grid layout */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {visibleItems.map((item, index) => (
          <motion.div
            key={`${currentIndex}-${index}`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
          >
            {/* 图标显示 / Icon display */}
            <span className="text-2xl">{item.icon}</span>
            {/* 项目名称 / Item name */}
            <span className="text-sm font-medium text-gray-700">{item.name}</span>
          </motion.div>
        ))}
      </div>
      
      {/* 导航控制器 - 仅在项目数量超过3个时显示 / Navigation controls - only show when items > 3 */}
      {items.length > 3 && (
        <div className="flex justify-center items-center mt-4 space-x-2">
          {/* 上一页按钮 / Previous button */}
          <Button
            variant="outline"
            size="sm"
            onClick={prevSlide}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          {/* 分页指示器 / Pagination indicators */}
          <div className="flex space-x-1">
            {Array.from({ length: Math.ceil(items.length / 3) }).map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? "bg-blue-500" : "bg-gray-300"
                }`}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </div>
          
          {/* 下一页按钮 / Next button */}
          <Button
            variant="outline"
            size="sm"
            onClick={nextSlide}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}

/**
 * 首页统计数据展示区域组件
 * Homepage statistics display section component
 * 
 * 此组件展示718.AI的四大核心优势：
 * 1. 生产力全面提升 - 展示AI工具如何提升工作效率
 * 2. AI工具类型 - 展示各种AI应用场景
 * 3. 服务质量 - 突出企业级可靠性
 * 4. 开源支持 - 强调开放生态理念
 * 
 * This component showcases 718.AI's four core advantages:
 * 1. Comprehensive Productivity Boost - How AI tools enhance work efficiency
 * 2. AI Tool Types - Various AI application scenarios
 * 3. Service Quality - Enterprise-grade reliability
 * 4. Open Source Support - Open ecosystem philosophy
 */
export default function StatsSection({}: StatsSectionProps) {
  // 获取翻译函数 / Get translation function
  const { t } = useTranslation()
  
  // 获取统计数据列表 / Get stats data list
  const statsList = t("stats") as StatCard[]
  
  // 获取区域标题数据 / Get section title data
  const sectionData = t("statsSection") as { title: string; subtitle: string }

  return (
    <section className="py-16 bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* 区域标题 / Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl mb-2">
            {sectionData.title}
          </h2>
          <p className="text-lg text-gray-600">
            {sectionData.subtitle}
          </p>
        </motion.div>

        {/* 统计卡片网格布局 / Stats cards grid layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {statsList.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              {/* 统计卡片 / Stats card */}
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                {/* 卡片头部 / Card header */}
                <CardHeader className="text-center">
                  <CardTitle className="text-xl font-semibold text-gray-900">
                    {stat.title}
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    {stat.subtitle}
                  </CardDescription>
                </CardHeader>
                
                {/* 卡片内容 - 包含滑动组件 / Card content - contains slider component */}
                <CardContent>
                  <StatSlider items={stat.items} />
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
