import Header from "@/components/header"
import Footer from "@/components/footer"
import B<PERSON><PERSON><PERSON>b<PERSON><PERSON> from "@/components/breadcrumb-nav"
import DocsList from "@/components/docs-list"
import DocsPageClient from "./docs-page-client"
import { getSortedDocsData, type DocMeta } from "@/lib/docs"

export default function DocsPage() {
  // 在服务端获取文档数据
  const docs = getSortedDocsData()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header />
      <DocsPageClient docs={docs} />
      <Footer />
    </div>
  )
}
