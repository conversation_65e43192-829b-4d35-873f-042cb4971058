<svg id="mermaid-rkv" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 1351.0078125px;" viewBox="0 0 1351.0078125 174" role="graphics-document document" aria-roledescription="flowchart-v2"><style>#mermaid-rkv{font-family:'lato',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',<PERSON><PERSON>,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#mermaid-rkv .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#mermaid-rkv .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#mermaid-rkv .error-icon{fill:#552222;}#mermaid-rkv .error-text{fill:#552222;stroke:#552222;}#mermaid-rkv .edge-thickness-normal{stroke-width:1px;}#mermaid-rkv .edge-thickness-thick{stroke-width:3.5px;}#mermaid-rkv .edge-pattern-solid{stroke-dasharray:0;}#mermaid-rkv .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-rkv .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-rkv .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-rkv .marker{fill:#333333;stroke:#333333;}#mermaid-rkv .marker.cross{stroke:#333333;}#mermaid-rkv svg{font-family:'lato',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:16px;}#mermaid-rkv p{margin:0;}#mermaid-rkv .label{font-family:'lato',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';color:#333;}#mermaid-rkv .cluster-label text{fill:#333;}#mermaid-rkv .cluster-label span{color:#333;}#mermaid-rkv .cluster-label span p{background-color:transparent;}#mermaid-rkv .label text,#mermaid-rkv span{fill:#333;color:#333;}#mermaid-rkv .node rect,#mermaid-rkv .node circle,#mermaid-rkv .node ellipse,#mermaid-rkv .node polygon,#mermaid-rkv .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-rkv .rough-node .label text,#mermaid-rkv .node .label text,#mermaid-rkv .image-shape .label,#mermaid-rkv .icon-shape .label{text-anchor:middle;}#mermaid-rkv .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-rkv .rough-node .label,#mermaid-rkv .node .label,#mermaid-rkv .image-shape .label,#mermaid-rkv .icon-shape .label{text-align:center;}#mermaid-rkv .node.clickable{cursor:pointer;}#mermaid-rkv .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaid-rkv .arrowheadPath{fill:#333333;}#mermaid-rkv .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-rkv .flowchart-link{stroke:#333333;fill:none;}#mermaid-rkv .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-rkv .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaid-rkv .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-rkv .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-rkv .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-rkv .cluster text{fill:#333;}#mermaid-rkv .cluster span{color:#333;}#mermaid-rkv div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:'lato',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-rkv .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-rkv rect.text{fill:none;stroke-width:0;}#mermaid-rkv .icon-shape,#mermaid-rkv .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-rkv .icon-shape p,#mermaid-rkv .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaid-rkv .icon-shape rect,#mermaid-rkv .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-rkv :root{--mermaid-font-family:'lato',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';}</style><g><marker id="mermaid-rkv_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="mermaid-rkv_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="mermaid-rkv_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="mermaid-rkv_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="mermaid-rkv_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="mermaid-rkv_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path d="M132,87L136.167,87C140.333,87,148.667,87,156.417,87.07C164.167,87.141,171.334,87.281,174.917,87.351L178.501,87.422" id="L_A_B_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#mermaid-rkv_flowchart-v2-pointEnd)"></path><path d="M300.5,87.5L313.355,87.417C326.211,87.333,351.922,87.167,377.049,87.083C402.177,87,426.721,87,438.993,87L451.266,87" id="L_B_C_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#mermaid-rkv_flowchart-v2-pointEnd)"></path><path d="M550.285,60L556.615,55.833C562.945,51.667,575.605,43.333,585.435,39.167C595.266,35,602.266,35,605.766,35L609.266,35" id="L_C_D_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#mermaid-rkv_flowchart-v2-pointEnd)"></path><path d="M550.285,114L556.615,118.167C562.945,122.333,575.605,130.667,585.435,134.833C595.266,139,602.266,139,605.766,139L609.266,139" id="L_C_E_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#mermaid-rkv_flowchart-v2-pointEnd)"></path><path d="M737.266,35L741.432,35C745.599,35,753.932,35,763.057,35C772.182,35,782.099,35,787.057,35L792.016,35" id="L_D_F_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#mermaid-rkv_flowchart-v2-pointEnd)"></path><path d="M737.266,139L741.432,139C745.599,139,753.932,139,761.599,139C769.266,139,776.266,139,779.766,139L783.266,139" id="L_E_G_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#mermaid-rkv_flowchart-v2-pointEnd)"></path><path d="M918.516,35L924.141,35C929.766,35,941.016,35,954.299,38.866C967.582,42.732,982.898,50.465,990.556,54.331L998.214,58.197" id="L_F_H_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#mermaid-rkv_flowchart-v2-pointEnd)"></path><path d="M927.266,139L931.432,139C935.599,139,943.932,139,955.757,135.134C967.582,131.268,982.898,123.535,990.556,119.669L998.214,115.803" id="L_G_H_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#mermaid-rkv_flowchart-v2-pointEnd)"></path><path d="M1133.266,87L1137.432,87C1141.599,87,1149.932,87,1157.599,87C1165.266,87,1172.266,87,1175.766,87L1179.266,87" id="L_H_I_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#mermaid-rkv_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel "></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(377.6328125, 87)"><g class="label" transform="translate(-52.6328125, -12)"><foreignObject width="105.265625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel "><p>HTTPS/TLS1.3</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel "></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel "></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel "></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel "></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel "></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel "></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel "></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default  " id="flowchart-A-0" transform="translate(70, 87)"><rect class="basic label-container" style="" x="-62" y="-27" width="124" height="54"></rect><g class="label" style="" transform="translate(-32, -12)"><rect></rect><foreignObject width="64" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel "><p>数据输入</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-B-1" transform="translate(241, 87)"><polygon points="59,0 118,-59 59,-118 0,-59" class="label-container" transform="translate(-59,59)"></polygon><g class="label" style="" transform="translate(-32, -12)"><rect></rect><foreignObject width="64" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel "><p>安全网关</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-C-3" transform="translate(509.265625, 87)"><rect class="basic label-container" style="" x="-54" y="-27" width="108" height="54"></rect><g class="label" style="" transform="translate(-24, -12)"><rect></rect><foreignObject width="48" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel "><p>应用层</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-D-5" transform="translate(675.265625, 35)"><rect class="basic label-container" style="" x="-62" y="-27" width="124" height="54"></rect><g class="label" style="" transform="translate(-32, -12)"><rect></rect><foreignObject width="64" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel "><p>传输加密</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-E-7" transform="translate(675.265625, 139)"><rect class="basic label-container" style="" x="-62" y="-27" width="124" height="54"></rect><g class="label" style="" transform="translate(-32, -12)"><rect></rect><foreignObject width="64" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel "><p>存储加密</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-F-9" transform="translate(857.265625, 35)"><rect class="basic label-container" style="" x="-61.25" y="-27" width="122.5" height="54"></rect><g class="label" style="" transform="translate(-31.25, -12)"><rect></rect><foreignObject width="62.5" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel "><p>AES-256</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-G-11" transform="translate(857.265625, 139)"><rect class="basic label-container" style="" x="-70" y="-27" width="140" height="54"></rect><g class="label" style="" transform="translate(-40, -12)"><rect></rect><foreignObject width="80" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel "><p>字段级加密</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-H-14" transform="translate(1055.265625, 87)"><rect class="basic label-container" style="" x="-78" y="-27" width="156" height="54"></rect><g class="label" style="" transform="translate(-48, -12)"><rect></rect><foreignObject width="96" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel "><p>密钥管理系统</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-I-16" transform="translate(1263.13671875, 87)"><rect class="basic label-container" style="" x="-79.87109375" y="-27" width="159.7421875" height="54"></rect><g class="label" style="" transform="translate(-49.87109375, -12)"><rect></rect><foreignObject width="99.7421875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel "><p>HSM硬件模块</p></span></div></foreignObject></g></g></g></g></g></svg>