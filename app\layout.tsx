import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { LanguageProvider } from "@/lib/i18n";
import { zh } from "@/lib/i18n/locales/zh";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  metadataBase: new URL("https://718.ai"),
  title: {
    default: zh.metadata.title,
    template: `%s - ${zh.metadata.title}`,
  },
  description: zh.metadata.description,
  openGraph: {
    title: zh.metadata.title,
    description: zh.metadata.description,
    url: "https://718.ai",
    siteName: "718.AI",
    images: [
      {
        url: "/images/718ai-8.png",
        width: 1200,
        height: 630,
        alt: "718.AI Logo",
      },
    ],
    locale: "zh_CN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: zh.metadata.title,
    description: zh.metadata.description,
    images: ["/images/718ai-8.png"],
  },
  icons: {
    icon: "/images/718ai-8.png",
    apple: "/images/718ai-8.png",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "Organization",
        name: "718.AI",
        url: "https://718.ai",
        logo: "https://718.ai/images/718ai-8.png",
      },
      {
        "@type": "WebSite",
        name: "718.AI",
        url: "https://718.ai",
        potentialAction: {
          "@type": "SearchAction",
          target: "https://718.ai/docs?q={search_term_string}",
          "query-input": "required name=search_term_string",
        },
      },
    ],
  };

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
        <LanguageProvider defaultLanguage="zh">
          {children}
          <Toaster />
        </LanguageProvider>
      </body>
    </html>
  );
}
