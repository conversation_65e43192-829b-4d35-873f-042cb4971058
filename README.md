This is a [Next.js](https://nextjs.org) project bootstrapped with [`ai hub website`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
# npm命令：
npm install | npm run dev | npm run start | npm run build
# or bun命令：
bun install | bun run dev | bun run start | bun run build
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>ei<PERSON>](https://vercel.com/font), a new font family for Vercel.

