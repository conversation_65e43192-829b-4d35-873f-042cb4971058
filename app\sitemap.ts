import { getSortedDocsData } from "@/lib/docs";
import { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
  const siteUrl = "https://718ai.cn";

  // Get all docs and map them to sitemap entries
  const docs = getSortedDocsData().map(({ slug, lastUpdated }) => ({
    url: `${siteUrl}/docs/${slug}`,
    lastModified: lastUpdated ? new Date(lastUpdated) : new Date(),
  }));

  // Define static routes
  const routes = ["", "/changelog", "/docs", "/features", "/pricing", "/register"].map(
    (route) => ({
      url: `${siteUrl}${route}`,
      lastModified: new Date(),
    })
  );

  return [...routes, ...docs];
}
