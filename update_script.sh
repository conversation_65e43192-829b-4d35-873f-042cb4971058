#!/bin/bash

# 应用更新脚本
# 功能：拉取最新代码 -> 构建Docker镜像 -> 更新容器

NAME=$(jq -r '.name' package.json | tr -d '@/' | tr '[:upper:]' '[:lower:]')
VERSION=$(jq -r '.version' package.json)
BRANCH="main"
GIT_REMOTE="origin"

# 打印变量
echo "分支: $BRANCH"
echo "Git远程仓库: $GIT_REMOTE"
echo "镜像名称: $NAME"
echo "版本: $VERSION"


echo "===> 步骤1/4: 从Git拉取最新代码..."
git fetch "$GIT_REMOTE"
git checkout "$BRANCH"
git pull "$GIT_REMOTE" "$BRANCH"

# 检查git pull是否成功
if [ $? -ne 0 ]; then
    echo "错误：Git拉取失败"
    exit 1
fi

echo "===> 步骤2/4: 生成docker-compose文件..."
cat <<EOF > docker-compose.yml
services:
  app:
    build: .
    image: ${NAME}:${VERSION}
    container_name: 718ai-website
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    networks:
      - app-net
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000', r => process.exit(r.statusCode === 200 ? 0 : 1))"]
      interval: 30s
      timeout: 5s
      retries: 3

networks:
  app-net:
    driver: bridge
EOF


echo "===> 步骤3/4: 构建Docker镜像并启动容器..."

# docker build -t "${NAME}:${VERSION}" .
docker-compose up -d --build
# sed -i '' "s|image:.*|image: ${NAME}:${VERSION}|" docker-compose.yml

echo "✅ 镜像构建完成: ${NAME}:${VERSION}"

# 检查构建是否成功
if [ $? -ne 0 ]; then
    echo "错误：Docker构建失败"
    exit 1
fi

echo "===> 步骤3/4: 检查容器是否启动成功..."
if [ $? -eq 0 ]; then
    echo "更新成功完成!"
    echo "当前运行的容器状态:"
    docker-compose ps
else
    echo "错误：容器启动失败"
    exit 1
fi


# 日志目录：/data/1panel/apps/openresty/openresty/www/sites/718ai.cn/log
# LANG="zh_CN.UTF-8" goaccess /data/1panel/apps/openresty/openresty/www/sites/718ai.cn/log/access.log-20250726 -o /root/report-20250726.html --log-format=COMBINED

