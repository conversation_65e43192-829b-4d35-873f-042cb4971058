# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
<!-- 此文件为 Claude Code 提供在该代码库中工作的指导 -->

## Available Commands / 可用命令

### Development / 开发命令
- `npm run dev` - Start development server at http://localhost:3000 (启动开发服务器)
- `npm run build` - Build production version (构建生产版本)
- `npm run start` - Start production server (启动生产服务器)
- `npm run lint` - Run ESLint for code quality checks (运行代码质量检查)

### Package Management / 包管理
Supports both npm and bun (支持 npm 和 bun):
- `npm install` / `bun install` - Install dependencies (安装依赖)
- `npm run [script]` / `bun run [script]` - Run package scripts (运行包脚本)

## Architecture Overview / 架构概览

This is a Next.js 14 website for 718.AI with the following key architectural patterns:
<!-- 这是一个基于 Next.js 14 的 718.AI 网站，具有以下关键架构模式 -->

### App Router Structure / App 路由结构
- Uses Next.js App Router (`app/` directory) (使用 Next.js App 路由器)
- File-based routing with nested layouts (基于文件的路由与嵌套布局)
- Pages: home (`page.tsx`), features, pricing, docs, changelog, register (页面包括首页、功能、定价、文档、更新日志、注册)

### Internationalization (i18n) / 国际化
- Custom i18n system in `lib/i18n/` (自定义国际化系统)
- `LanguageProvider` context wraps the entire app (语言提供器上下文包装整个应用)
- Default language is Chinese (`"zh"`) (默认语言为中文)
- Translations stored in `lib/i18n/locales/` (en.ts, zh.ts) (翻译文件存储位置)
- Use `useTranslation()` hook for component translations (使用翻译钩子)
- Use `useLanguage()` for language switching (使用语言切换钩子)

### UI Component System / UI 组件系统
- Built on Radix UI primitives with custom styling (基于 Radix UI 基础组件)
- Components in `components/ui/` follow shadcn/ui patterns (遵循 shadcn/ui 模式)
- Uses `class-variance-authority` (cva) for variant styling (使用 cva 进行变体样式管理)
- Tailwind CSS with custom design tokens (使用自定义设计令牌的 Tailwind CSS)
- Toast notifications via Sonner (通过 Sonner 实现提示通知)

### Documentation System / 文档系统
- Markdown-based docs in `docs/` directory (基于 Markdown 的文档)
- `lib/docs.ts` handles file parsing with gray-matter (文件解析处理)
- Markdown processing pipeline: remark → remarkGfm → remarkRehype → rehypeSlug → rehypeAutolinkHeadings (Markdown 处理管道)
- Dynamic routing via `app/docs/[...slug]/` (动态路由)
- Custom `MarkdownRenderer` component with code copy functionality (自定义 Markdown 渲染器)

### Styling System / 样式系统
- Tailwind CSS with custom configuration (自定义配置的 Tailwind CSS)
- CSS custom properties for theming (HSL color system) (HSL 颜色系统主题)
- Typography plugin for markdown content (Markdown 内容排版插件)
- Responsive design with mobile-first approach (移动端优先的响应式设计)
- Custom sidebar color tokens (自定义侧边栏颜色令牌)

### State Management / 状态管理
- React Context for language state (React 上下文管理语言状态)
- No external state management library (无外部状态管理库)
- Toast state managed by useToast hook (Toast 状态管理)

### Configuration / 配置
- Site config in `lib/config.ts` with environment variable fallbacks (网站配置文件)
- External links to 718.AI console (login/register URLs) (外部链接配置)
- TypeScript with strict configuration (严格的 TypeScript 配置)

## Key Dependencies / 关键依赖

- **Next.js 14**: React framework with App Router (带 App 路由的 React 框架)
- **Radix UI**: Headless component primitives (无头组件基础库)
- **Tailwind CSS**: Utility-first styling (实用程序优先的样式框架)
- **Framer Motion**: Animation library (动画库)
- **remark/rehype**: Markdown processing ecosystem (Markdown 处理生态系统)
- **gray-matter**: Frontmatter parsing (前置数据解析)
- **Lucide React**: Icon library (图标库)
- **TypeScript**: Type safety (类型安全)

## Development Notes / 开发注意事项

- Components use TypeScript with proper prop interfaces (组件使用 TypeScript 和正确的属性接口)
- Markdown files support frontmatter (title, description, category, order, tags, lastUpdated) (Markdown 文件支持前置数据)
- Image assets in `public/images/` including 718ai-8.png logo (图片资源位置)
- Fonts: Geist Sans and Geist Mono loaded locally (本地加载的字体)
- Default meta description: "Generated by 718.AI Website" (默认元描述)

## Stats Section / 统计数据区域

The homepage features a stats section with four main categories (首页统计区域包含四个主要类别):

1. **生产力全面提升 / Comprehensive Productivity Boost**: Shows how AI tools enhance work efficiency (展示 AI 工具如何提升工作效率)
2. **AI工具类型 / AI Tool Types**: Displays various AI application scenarios (展示各种 AI 应用场景)
3. **服务质量 / Service Quality**: Highlights enterprise-grade reliability (突出企业级可靠性)
4. **开源支持 / Open Source Support**: Emphasizes open ecosystem philosophy (强调开放生态理念)

Each category is implemented as a card with:
- Title and subtitle (标题和副标题)
- Sliding carousel for items with icons (带图标的滑动轮播项目)
- Navigation controls for items > 3 (超过3个项目时的导航控制)

The component is located at `components/stats-section.tsx` and uses translations from `lib/i18n/locales/` for both Chinese and English content.
(组件位于 `components/stats-section.tsx`，使用来自 `lib/i18n/locales/` 的中英文翻译)