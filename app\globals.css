@tailwind base;
@tailwind components;
@tailwind utilities;

/* highlight.js 代码主题（github-dark）
   直接从 node_modules 加载，避免远程 CDN 造成的 blob: 加载失败  */

/* Markdown 样式 */
@layer components {

  /* rehype-pretty-code 生成的代码块容器 */
  [data-rehype-pretty-code-fragment] {
    @apply relative mb-4 rounded-lg overflow-hidden border border-gray-200;
    background-color: #282c34;
    /* 默认背景色，与 github-dark 主题匹配 */
    color: #abb2bf;
    /* 默认文本颜色 */
  }

  /* 代码块头部，包含语言和复制按钮 */
  [data-rehype-pretty-code-fragment]>.code-block-header {
    @apply flex items-center justify-between px-4 py-2 bg-gray-800 text-gray-300 text-sm;
  }

  [data-rehype-pretty-code-fragment]>.code-block-header>.code-block-lang {
    @apply font-mono text-xs uppercase tracking-wide;
  }

  [data-rehype-pretty-code-fragment]>.code-block-header>.code-copy-btn {
    @apply flex items-center space-x-1 px-2 py-1 rounded text-xs hover:bg-gray-700 transition-colors;
  }

  [data-rehype-pretty-code-fragment]>.code-block-header>.code-copy-btn:hover {
    @apply text-white;
  }

  /* 代码内容区域 */
  [data-rehype-pretty-code-fragment]>pre {
    @apply p-4 overflow-x-auto text-sm leading-relaxed;
    font-family: "Fira Code", "Monaco", "Cascadia Code", "Roboto Mono", monospace;
    white-space: pre;
    tab-size: 2;
    background-color: inherit;
    /* 继承父容器背景色 */
    color: inherit;
    /* 继承父容器文本颜色 */
    white-space: pre-wrap;
    /* Ensure long lines wrap */
    word-break: break-all;
    /* Allow breaking long words */
  }

  /* Individual lines within the code block */
  [data-rehype-pretty-code-fragment]>pre>code>.line {
    display: block;
    /* Make each line span a block element */
    min-height: 1em;
    /* Ensure empty lines are visible */
    white-space: pre-wrap;
    /* Ensure wrapping within lines if needed */
  }

  /* 代码行 */
  [data-rehype-pretty-code-fragment]>pre>code {
    @apply bg-transparent text-inherit;
    font-family: inherit;
    white-space: pre;
  }

  /* 高亮行 */
  .line--highlighted {
    background-color: rgba(255, 255, 255, 0.1);
    /* 浅色背景高亮 */
    display: block;
    margin-right: -1rem;
    margin-left: -1rem;
    padding-right: 1rem;
    padding-left: 1rem;
    border-left: 2px solid #61afef;
    /* 左侧蓝色边框 */
  }

  /* 行内代码样式 */
  .inline-code {
    @apply bg-gray-100 text-gray-800 px-1.5 py-0.5 rounded text-sm font-mono;
  }

  /* 表格样式 */
  .table-wrapper {
    @apply overflow-x-auto mb-4;
  }

  .markdown-table {
    @apply w-full border-collapse border border-gray-200 text-sm;
  }

  .markdown-table th {
    @apply bg-gray-50 border border-gray-200 px-4 py-2 text-left font-semibold;
  }

  .markdown-table td {
    @apply border border-gray-200 px-4 py-2;
  }

  /* 标题锚点样式 */
  .header-anchor {
    @apply opacity-0 ml-2 text-blue-500 no-underline transition-opacity;
  }

  h1:hover .header-anchor,
  h2:hover .header-anchor,
  h3:hover .header-anchor,
  h4:hover .header-anchor,
  h5:hover .header-anchor,
  h6:hover .header-anchor {
    @apply opacity-100;
  }

  /* 确保代码块在小屏幕上也能正确显示 */
  @media (max-width: 768px) {
    [data-rehype-pretty-code-fragment] {
      @apply mx-0;
    }

    [data-rehype-pretty-code-fragment]>pre {
      @apply text-xs;
    }
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}