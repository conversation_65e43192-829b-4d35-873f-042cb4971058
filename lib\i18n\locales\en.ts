import type { Translations } from "../types";

export const en: Translations = {
  metadata: {
    title: "718.AI, Empowering Every Imagination With AI",
    description: "Access cutting-edge AI tools and APIs with multiple AI models. Pay only for what you use and integrate AI capabilities into your applications quickly.",
  },
  common: {
    signIn: "Sign In",
    signUp: "Sign Up",
    getStarted: "Get Started",
    learnMore: "Learn More",
    backToHome: "Back to Home",
    loading: "Loading...",
    error: "Error",
    success: "Success",
    cancel: "Cancel",
    confirm: "Confirm",
    save: "Save",
    edit: "Edit",
    delete: "Delete",
    search: "Search",
    noResults: "No results found",
    lastUpdated: "Last Updated",
  },
  navigation: {
    home: "Home",
    features: "Features",
    pricing: "Pricing",
    demo: "Demo",
    changelog: "Changelog",
    docs: "Docs",
  },
  hero: {
    title: "718.AI, Empowering Every Imagination With AI",
    subtitle: "Pay-as-you-go, No Subscription",
    description: "Access cutting-edge AI tools and APIs with multiple AI models. Pay only for what you use and integrate AI capabilities into your applications quickly.",
    cta: "Get Started",
    demo: "View Demo",
  },
  quickStart: {
    title: "Quick Start",
    subtitle: "Integrate AI capabilities in three steps",
    steps: [
      {
        title: "Sign Up",
        description: "Register for free and get your API key",
        icon: "1",
      },
      {
        title: "Choose Model",
        description: "Select the most suitable AI model",
        icon: "2",
      },
      {
        title: "Start Calling",
        description: "Integrate AI features with simple API calls",
        icon: "3",
      },
    ],
  },
  features: {
    title: "Why Build with 718.AI?",
    subtitle: "We handle the complexity, so you can focus on innovation.",
    description: "We provide comprehensive AI API solutions covering text generation, image processing, speech services, and more, helping your applications quickly integrate AI capabilities.",
    items: [
      {
        title: "All Leading Models, One API",
        description: "Access the best models from OpenAI, Anthropic, Meta, Google, and top open-source projects without the hassle of managing multiple integrations.",
      },
      {
        title: "Unified & Simple Interface",
        description: "Drastically simplify your codebase. Switch between models with a single parameter change, not a new library.",
      },
      {
        title: "Always Cutting-Edge",
        description: "We constantly add the latest models and features, so you can stay at the forefront of AI without rewriting your code.",
      },
    ],
  },
  pricing: {
    title: "Pricing Plans",
    subtitle: "Transparent pay-as-you-go pricing, pay only for what you use",
    description: "We offer flexible pay-as-you-go pricing with no subscription fees, allowing you to use AI services flexibly based on your actual needs.",
    calculator: "Cost Calculator",
  },
  changelog: {
    title: "Changelog",
    subtitle: "Stay updated with our latest features and improvements",
    description: "We continuously improve and optimize our AI API services to provide you with a better user experience. Check out our latest updates and feature releases.",
  },
  docs: {
    title: "Documentation",
    description: "Find detailed information, code examples, and best practices on how to use our AI APIs.",
    searchPlaceholder: "Search documentation...",
    backToDocs: "Back to Docs List",
    tableOfContents: "Table of Contents",
    relatedDocs: "Related Documents",
    categories: {
      "getting-started": "Getting Started",
      "api-reference": "API Reference",
      guides: "Guides",
      examples: "Examples",
      troubleshooting: "Troubleshooting",
      legal: "Legal",
      uncategorized: "Uncategorized",
    },
  },
  footer: {
    company: {
      name: "718.AI",
      description: "Empowering Every Imagination With AI",
    },
    links: {
      product: {
        title: "Product",
        items: [
          { name: "Features", href: "/features" },
          { name: "Pricing", href: "/pricing" },
          { name: "API Docs", href: "/docs" },
        ],
      },
      company: {
        title: "Company",
        items: [
          { name: "Changelog", href: "/changelog" },
        ],
      },
      support: {
        title: "Support",
        items: [],
      },
      legal: {
        title: "Legal",
        items: [
          { name: "Terms of Service", href: "/docs/terms" },
          { name: "Privacy Policy", href: "/docs/privacy" },
        ],
      },
    },
    contact: {
      title: "Contact",
      email: "<EMAIL>",
      phone: "+****************",
      address: "Haizhu District, Pazhou West Area, Guangzhou City, Guangdong Province, China",
    },
    social: {
      title: "Follow Us",
    },
    copyright: "© 2025 718.AI. All rights reserved.",
    icp: "",
  },
  cta: {
    title: "Ready to Get Started?",
    description: "Sign up now and get free API credits",
    button: "Start Free",
  },
  statsSection: {
    title: "Our Advantages",
    subtitle: "Comprehensive AI Service Capabilities"
  },
  stats: [
    {
      title: "Comprehensive Productivity Boost",
      subtitle: "AI Tools Empowering Efficient Work",
      items: [
        { name: "Automated Task Processing", icon: "🤖" },
        { name: "Intelligent Content Generation", icon: "✨" },
        { name: "Efficiency Tool Integration", icon: "⚙️" },
        { name: "Collaboration Workflow Optimization", icon: "🔄" },
        { name: "Reduced Time Costs", icon: "⏱️" },
        { name: "Enhanced Innovation Capacity", icon: "🚀" }
      ]
    },
    {
      title: "AI Tool Types",
      subtitle: "Comprehensive AI Application Coverage",
      items: [
        { name: "Text Generation", icon: "💬" },
        { name: "Image Processing", icon: "🎨" },
        { name: "Voice Services", icon: "🎤" },
        { name: "Code Assistance", icon: "💻" },
        { name: "Data Analysis", icon: "📊" },
        { name: "Translation Services", icon: "🌐" }
      ]
    },
    {
      title: "Service Quality",
      subtitle: "Enterprise-Grade Reliability",
      items: [
        { name: "99.9% Service Uptime", icon: "⚡" },
        { name: "Millisecond Response", icon: "🚀" },
        { name: "Global CDN Acceleration", icon: "🌏" },
        { name: "Enterprise Security", icon: "🔒" },
        { name: "Real-time Monitoring", icon: "📈" },
        { name: "Elastic Scaling", icon: "📱" }
      ]
    },
    {
      title: "Open Source Support",
      subtitle: "Embracing Open Ecosystem",
      items: [
        { name: "Open Source Models", icon: "🔓" },
        { name: "Community Contributions", icon: "❤️" },
        { name: "Open API Standards", icon: "📋" },
        { name: "Open Documentation", icon: "📖" },
        { name: "Developer Tools", icon: "🔧" },
        { name: "Ecosystem Partnerships", icon: "🤝" }
      ]
    }
  ],
  faq: {
    title: "Frequently Asked Questions",
    items: [
      {
        question: "How is billing calculated?",
        answer: "We use a pay-as-you-go billing model based on your actual API calls and data usage, with no minimum consumption requirements.",
      },
      {
        question: "Is there a free tier?",
        answer: "Yes, all new users receive $10 in free credits upon registration, which is sufficient for testing and evaluating our services.",
      },
      {
        question: "What payment methods are supported?",
        answer: "We support various payment methods including credit cards, PayPal, and bank transfers for enterprise customers.",
      },
    ],
  },
  newsletter: {
    title: "Subscribe to Updates",
    description: "Get notified about new feature releases and important updates",
    placeholder: "Enter your email address",
    button: "Subscribe",
  },
  registerPage: {
    leftPane: {
      title: "One API for All Leading AI Models",
      subtitle: "Stop managing dozens of API keys. Access the latest models from OpenAI, Google, Anthropic, and more with a single integration.",
      testimonial: "We believe that code is creative. And that if you give developers tools, they will do amazing things.",
      testimonialAuthor: "Jeff Lawson, CEO of Twilio",
    },
    form: {
      title: "Create an account",
      subtitle: "Already have an account?",
      signInLink: "Sign in",
      separator: "Or continue with email",
      emailLabel: "Email address",
      verificationCodeLabel: "Email Verification Code",
      sendCodeButton: "Send Code",
      sendingCodeButton: "Sending...",
      resendCodeButton: "Resend",
      resendCodeCountdown: "Resend in {countdown}s",
      usernameLabel: "Username",
      passwordLabel: "Password",
      confirmPasswordLabel: "Confirm Password",
      submitButton: "Create Free Account",
      loadingButton: "Creating Account...",
      termsPrefix: "By clicking continue, you agree to our",
      termsLink: "Terms of Service",
      and: "and",
      privacyLink: "Privacy Policy",
    },
    success: {
      message: "Registration successful! You can now log in.",
      redirectMessagePrefix: "You will be redirected to the login page in ",
      redirectMessageSuffix: " seconds...",
      codeSentSuccess: "Verification code sent to your email"
    },
    errors: {
      passwordMismatch: "Passwords do not match. Please try again.",
      passwordTooShort: "Password must be at least 8 characters long.",
      emailInUse: "This email address is already registered.",
      codeRequired: "Please enter verification code",
      codeInvalid: "Invalid verification code, please try again",
      emailRequired: "Please enter email address first",
      emailInvalid: "Please enter a valid email address",
    }
  },
};
