"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useTranslation, useLanguage } from "@/lib/i18n"
import { <PERSON><PERSON><PERSON>, Eye, EyeOff, Loader2 } from "lucide-react"
import { siteConfig } from "@/lib/config"
import { getLocalizedDocPath } from "@/lib/doc-utils"

// A more accurate, single-color Google icon
const GoogleIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path d="M21.35,11.1H12.18V13.83H18.69C18.36,17.64 15.19,19.27 12.19,19.27C8.36,19.27 5,16.25 5,12C5,7.9 8.2,4.73 12.19,4.73C14.03,4.73 15.6,5.33 16.59,6.57L18.83,4.47C17.22,2.91 15.03,2 12.19,2C6.81,2 3.06,6.21 3.06,12C3.06,17.73 6.7,22 12.19,22C17.62,22 21.5,18.33 21.5,12.33C21.5,11.8 21.45,11.46 21.35,11.1Z" />
  </svg>
)

export default function RegisterPage() {
  const { t } = useTranslation()
  const { language } = useLanguage()
  const [email, setEmail] = useState("")
  const [username, setUsername] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [countdown, setCountdown] = useState(5)
  const [isCodeSent, setIsCodeSent] = useState(false)
  const [codeSendLoading, setCodeSendLoading] = useState(false)
  const [codeCountdown, setCodeCountdown] = useState(0)
  const router = useRouter()

  useEffect(() => {
    let timer: NodeJS.Timeout | undefined;
    let countdownInterval: NodeJS.Timeout | undefined;

    if (success) {
      setCountdown(5); // Reset countdown on new success
      
      // Start countdown timer for UI
      countdownInterval = setInterval(() => {
        setCountdown(prev => (prev > 0 ? prev - 1 : 0));
      }, 1000);

      // Set timeout for redirection
      timer = setTimeout(() => {
        router.push(siteConfig.links.login);
      }, 5000);
    }

    // Cleanup function to clear timers
    return () => {
      if (timer) clearTimeout(timer);
      if (countdownInterval) clearInterval(countdownInterval);
    };
  }, [success, router]);

  // Handle verification code countdown
  useEffect(() => {
    let codeCountdownInterval: NodeJS.Timeout | undefined;

    if (codeCountdown > 0) {
      codeCountdownInterval = setInterval(() => {
        setCodeCountdown(prev => prev - 1);
      }, 1000);
    }

    return () => {
      if (codeCountdownInterval) clearInterval(codeCountdownInterval);
    };
  }, [codeCountdown]);

  // Email validation helper
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Send verification code
  const handleSendCode = async () => {
    if (!email.trim()) {
      setError(t("registerPage.errors.emailRequired"));
      return;
    }

    if (!isValidEmail(email)) {
      setError(t("registerPage.errors.emailInvalid"));
      return;
    }

    setCodeSendLoading(true);
    setError(null);

    try {
      const response = await fetch('https://api-dev.718ai.cn/auth/send-verification-code', {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          type: 'register'
        }),
      });

      const data = await response.json();

      if (data.success) {
        setIsCodeSent(true);
        setCodeCountdown(60); // 60 seconds countdown
        setSuccess(t("registerPage.success.codeSentSuccess"));

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError(data.message || "发送验证码失败，请重试");
      }
    } catch (error) {
      console.error('Send verification code error:', error);
      setError("发送验证码失败，请重试");
    } finally {
      setCodeSendLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    // --- Client-side validation ---
    if (!email.trim()) {
      setError(t("registerPage.errors.emailRequired"));
      return;
    }
    if (!isValidEmail(email)) {
      setError(t("registerPage.errors.emailInvalid"));
      return;
    }
    if (!verificationCode.trim()) {
      setError(t("registerPage.errors.codeRequired"));
      return;
    }
    // Basic verification code format validation (6 digits)
    if (verificationCode.length !== 6 || !/^\d{6}$/.test(verificationCode)) {
      setError(t("registerPage.errors.codeInvalid"));
      return;
    }
    if (password !== confirmPassword) {
      setError(t("registerPage.errors.passwordMismatch"));
      return;
    }
    if (password.length < 8) {
      setError(t("registerPage.errors.passwordTooShort"));
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('https://api-dev.718ai.cn/auth/register-with-code', {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          password: password,
          username: username || email.split('@')[0], // Use email prefix as default username
          verification_code: verificationCode
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(t("registerPage.success.message"));
        setEmail('');
        setUsername('');
        setVerificationCode('');
        setPassword('');
        setConfirmPassword('');
        setIsCodeSent(false);
        setCodeCountdown(0);
      } else {
        setError(data.message || "注册失败，请重试");
      }

    } catch (err: any) {
      console.error('Registration submission error:', err);
      setError("注册失败，请检查网络连接后重试");
    } finally {
      setLoading(false);
    }
  }


  return (
    <div className="min-h-screen w-full grid grid-cols-1 lg:grid-cols-2">
      {/* Left Pane: Value Proposition */}
      <div className="hidden lg:flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 to-slate-800 text-white p-12">
        <Link href="/" className="self-start mb-12">
          <div className="flex items-center space-x-2">
            <Image src="/images/718ai-8.png" alt="718AI Logo" width={40} height={40} className="h-10 w-10 rounded-lg object-cover" />
            <span className="text-2xl font-bold">718.AI</span>
          </div>
        </Link>
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">
            {t("registerPage.leftPane.title")}
          </h1>
          <p className="text-blue-100 max-w-lg">
            {t("registerPage.leftPane.subtitle")}
          </p>
        </div>
        <div className="mt-12 w-full max-w-lg">
            <div className="bg-white/10 p-6 rounded-lg">
                <blockquote className="text-lg italic">
                {t("registerPage.leftPane.testimonial")}
                </blockquote>
                <p className="mt-4 font-semibold text-right">- {t("registerPage.leftPane.testimonialAuthor")}</p>
            </div>
        </div>
      </div>

      {/* Right Pane: Registration Form */}
      <div className="flex items-center justify-center p-6 sm:p-12 bg-slate-50">
        <div className="w-full max-w-md space-y-6">
          <div className="text-center lg:text-left">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900">
              {t("registerPage.form.title")}
            </h2>
            <p className="mt-2 text-gray-600">
              {t("registerPage.form.subtitle")}{" "}
              <Link href={siteConfig.links.login} className="font-medium text-blue-600 hover:underline">
                {t("registerPage.form.signInLink")}
              </Link>
            </p>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md" role="alert">
              <p>{error}</p>
            </div>
          )}
          
          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-md" role="alert">
              <p>{success}</p>
              <p className="text-sm mt-1">
                {t("registerPage.success.redirectMessagePrefix")}
                <span className="font-bold">{countdown}</span>
                {t("registerPage.success.redirectMessageSuffix")}
              </p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <Button variant="outline" className="w-full" disabled={loading || !!success}>
              <GoogleIcon className="mr-2 h-5 w-5" />
              Google
            </Button>
            <Button variant="outline" className="w-full" disabled={loading || !!success}>
              <Github className="mr-2 h-5 w-5" />
              GitHub
            </Button>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-slate-50 px-2 text-gray-500">
                {t("registerPage.form.separator")}
              </span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                {t("registerPage.form.emailLabel")}
              </label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  autoComplete="email"
                  required
                  disabled={loading || !!success}
                  className="flex-1"
                  placeholder="<EMAIL>"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSendCode}
                  disabled={loading || !!success || codeSendLoading || codeCountdown > 0}
                  className="whitespace-nowrap"
                >
                  {codeSendLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {codeCountdown > 0
                    ? t("registerPage.form.resendCodeCountdown").replace("{countdown}", codeCountdown.toString())
                    : isCodeSent
                      ? t("registerPage.form.resendCodeButton")
                      : codeSendLoading
                        ? t("registerPage.form.sendingCodeButton")
                        : t("registerPage.form.sendCodeButton")
                  }
                </Button>
              </div>
            </div>

            <div>
              <label htmlFor="verification-code" className="block text-sm font-medium text-gray-700">
                {t("registerPage.form.verificationCodeLabel")}
              </label>
              <Input
                id="verification-code"
                name="verification-code"
                type="text"
                value={verificationCode}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                  setVerificationCode(value);
                }}
                maxLength={6}
                required
                disabled={loading || !!success}
                className="mt-1"
                placeholder="123456"
              />
            </div>

            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                {t("registerPage.form.usernameLabel")}
              </label>
              <Input
                id="username"
                name="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                autoComplete="username"
                disabled={loading || !!success}
                className="mt-1"
                placeholder={t("registerPage.form.usernameLabel")}
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                {t("registerPage.form.passwordLabel")}
              </label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  autoComplete="new-password"
                  required
                  disabled={loading || !!success}
                  className="mt-1 pr-10"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>
            
            <div>
              <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700">
                {t("registerPage.form.confirmPasswordLabel")}
              </label>
              <div className="relative">
                <Input
                  id="confirm-password"
                  name="confirm-password"
                  type={showConfirmPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  autoComplete="new-password"
                  required
                  disabled={loading || !!success}
                  className="mt-1 pr-10"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700"
                >
                  {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            <Button type="submit" className="w-full" size="lg" disabled={loading || !!success}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {loading ? t("registerPage.form.loadingButton") : t("registerPage.form.submitButton")}
            </Button>
          </form>
          <p className="px-8 text-center text-sm text-gray-500">
            {t("registerPage.form.termsPrefix")}{" "}
            <Link href={`/docs/${getLocalizedDocPath("terms", language)}`} className="underline hover:text-blue-600">
              {t("registerPage.form.termsLink")}
            </Link>{" "}
            {t("registerPage.form.and")}{" "}
            <Link href={`/docs/${getLocalizedDocPath("privacy", language)}`} className="underline hover:text-blue-600">
              {t("registerPage.form.privacyLink")}
            </Link>
            .
          </p>
        </div>
      </div>
    </div>
  )
}
