import type { Translations } from "../types";

export const zh: Translations = {
  metadata: {
    title: "718.AI，让AI赋能每一份想象",
    description: "提供最先进的AI工具和API服务，支持多种AI模型，按实际使用量计费，让您的应用快速集成AI能力。",
  },
  common: {
    signIn: "登录",
    signUp: "注册",
    getStarted: "立即开始",
    learnMore: "了解更多",
    backToHome: "返回首页",
    loading: "加载中...",
    error: "错误",
    success: "成功",
    cancel: "取消",
    confirm: "确认",
    save: "保存",
    edit: "编辑",
    delete: "删除",
    search: "搜索",
    noResults: "没有找到结果",
    lastUpdated: "最后更新",
  },
  navigation: {
    home: "首页",
    features: "产品",
    pricing: "定价",
    demo: "演示",
    changelog: "更新日志",
    docs: "文档",
  },
  hero: {
    title: "718.AI，让AI赋能每一份想象",
    subtitle: "按需付费，无需订阅",
    description: "提供最先进的AI工具和API服务，支持多种AI模型，按实际使用量计费，让您的应用快速集成AI能力。",
    cta: "立即开始",
    demo: "查看演示",
  },
  quickStart: {
    title: "快速开始",
    subtitle: "三步即可集成AI能力",
    steps: [
      {
        title: "注册账户",
        description: "免费注册并获取API密钥",
        icon: "1",
      },
      {
        title: "选择模型",
        description: "从多种AI模型中选择最适合的",
        icon: "2",
      },
      {
        title: "开始调用",
        description: "使用简单的API调用集成AI功能",
        icon: "3",
      },
    ],
  },
  features: {
    title: "为什么选择 718.AI？",
    subtitle: "我们处理繁杂，您只需专注创新。",
    description: "我们提供全面的AI API解决方案，涵盖文本生成、图像处理、语音服务等多个领域，助力您的应用快速集成AI能力。",
    items: [
      {
        title: "顶尖模型，一个入口",
        description: "轻松访问来自OpenAI、Anthropic、Meta、Google的顶尖模型及优秀的开源模型，无需管理多个平台。",
      },
      {
        title: "统一简洁的调用",
        description: "极大简化您的代码库。切换模型只需修改一个参数，而不是重写整个集成方案。",
      },
      {
        title: "永远前沿",
        description: "我们持续集成最新的模型和功能，让您的应用永远站在AI技术的最前沿，无需担心技术过时。",
      },
    ],
  },
  pricing: {
    title: "定价方案",
    subtitle: "透明的按量计费，只为您实际使用的服务付费",
    description: "我们提供灵活的按量付费模式，无需订阅费用，让您可以根据实际需求灵活使用AI服务。",
    calculator: "成本计算器",
  },
  changelog: {
    title: "更新日志",
    subtitle: "了解我们最新的功能更新和改进",
    description: "我们持续改进和优化我们的AI API服务，为您提供更好的使用体验。查看我们的最新更新和功能发布。",
  },
  docs: {
    title: "文档",
    description: "查找关于如何使用我们AI API的详细信息、代码示例和最佳实践。",
    searchPlaceholder: "搜索文档...",
    backToDocs: "返回文档列表",
    tableOfContents: "目录",
    relatedDocs: "相关文档",
    categories: {
      "getting-started": "入门指南",
      "api-reference": "API参考",
      guides: "使用指南",
      examples: "代码示例",
      troubleshooting: "故障排除",
      legal: "法律条款",
      uncategorized: "未分类",
    },
  },
  footer: {
    company: {
      name: "718.AI",
      description: "让AI赋能每一份想象",
    },
    links: {
      product: {
        title: "产品",
        items: [
          { name: "功能特性", href: "/features" },
          { name: "定价方案", href: "/pricing" },
          { name: "API文档", href: "/docs" },
        ],
      },
      company: {
        title: "公司",
        items: [
          { name: "更新日志", href: "/changelog" },
        ],
      },
      support: {
        title: "支持",
        items: [],
      },
      legal: {
        title: "法律",
        items: [
          { name: "服务条款", href: "/docs/terms" },
          { name: "隐私政策", href: "/docs/privacy" },
        ],
      },
    },
    contact: {
      title: "联系我们",
      email: "<EMAIL>",
      phone: "+86 ************",
      address: "中国广东省广州市海珠区琶洲西区",
    },
    social: {
      title: "关注我们",
    },
    copyright: "© 2025 718.AI. 保留所有权利。",
    icp: "",
  },
  cta: {
    title: "准备好开始了吗？",
    description: "立即注册，获取免费API调用额度",
    button: "免费开始",
  },
  statsSection: {
    title: "我们的优势",
    subtitle: "全方位的AI服务能力展示"
  },
  stats: [
    {
      title: "生产力全面提升",
      subtitle: "智能工具赋能高效工作",
      items: [
        { name: "自动化任务处理", icon: "🤖" },
        { name: "智能内容生成", icon: "✨" },
        { name: "效率工具集成", icon: "⚙️" },
        { name: "协作流程优化", icon: "🔄" },
        { name: "时间成本降低", icon: "⏱️" },
        { name: "创新能力增强", icon: "🚀" }
      ]
    },
    {
      title: "AI工具类型",
      subtitle: "全面覆盖AI应用场景",
      items: [
        { name: "文本生成", icon: "💬" },
        { name: "图像处理", icon: "🎨" },
        { name: "语音服务", icon: "🎤" },
        { name: "代码助手", icon: "💻" },
        { name: "数据分析", icon: "📊" },
        { name: "翻译服务", icon: "🌐" }
      ]
    },
    {
      title: "服务质量",
      subtitle: "企业级可靠保障",
      items: [
        { name: "99.9% 服务可用性", icon: "⚡" },
        { name: "毫秒级响应速度", icon: "🚀" },
        { name: "全球CDN加速", icon: "🌏" },
        { name: "企业级安全", icon: "🔒" },
        { name: "实时监控", icon: "📈" },
        { name: "弹性扩展", icon: "📱" }
      ]
    },
    {
      title: "开源支持",
      subtitle: "拥抱开放生态",
      items: [
        { name: "开源模型集成", icon: "🔓" },
        { name: "社区贡献", icon: "❤️" },
        { name: "开放API标准", icon: "📋" },
        { name: "技术文档开源", icon: "📖" },
        { name: "开发者工具", icon: "🔧" },
        { name: "生态合作", icon: "🤝" }
      ]
    }
  ],
  faq: {
    title: "常见问题",
    items: [
      {
        question: "如何计费？",
        answer: "我们采用按量计费模式，根据您实际使用的API调用次数和数据量进行计费，无最低消费要求。",
      },
      {
        question: "是否有免费额度？",
        answer: "是的，所有新用户注册后可获得¥50的免费使用额度，足够您测试和评估我们的服务。",
      },
      {
        question: "支持哪些支付方式？",
        answer: "我们支持支付宝、微信支付、银行卡等多种支付方式，同时支持企业对公转账。",
      },
    ],
  },
  newsletter: {
    title: "订阅更新通知",
    description: "第一时间获取最新功能发布和重要更新通知",
    placeholder: "输入您的邮箱地址",
    button: "订阅",
  },
  registerPage: {
    leftPane: {
      title: "一个API，调用所有顶尖AI模型",
      subtitle: "告别管理数十个API密钥的烦恼。通过单一集成，即可访问来自OpenAI、谷歌、Anthropic等公司的最新模型。",
      testimonial: "我们相信代码是创造性的。只要你给开发者提供了工具，他们就能创造出令人惊叹的东西。",
      testimonialAuthor: "Jeff Lawson, Twilio CEO",
    },
    form: {
      title: "创建账户",
      subtitle: "已经有账户了？",
      signInLink: "直接登录",
      separator: "或使用邮箱注册",
      emailLabel: "邮箱地址",
      passwordLabel: "密码",
      confirmPasswordLabel: "确认密码",
      submitButton: "创建免费账户",
      loadingButton: "正在创建账户...",
      termsPrefix: "点击继续，即表示您同意我们的",
      termsLink: "服务条款",
      and: "和",
      privacyLink: "隐私政策",
    },
    success: {
      message: "注册成功！现在可以登录了。",
      redirectMessagePrefix: "将在 ",
      redirectMessageSuffix: " 秒后自动跳转到登录页面..."
    },
    errors: {
      passwordMismatch: "两次输入的密码不一致，请重试。",
      passwordTooShort: "密码长度至少需要8个字符。",
      emailInUse: "该邮箱地址已被注册。",
    }
  },
};
