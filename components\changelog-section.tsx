"use client"

import { motion } from "framer-motion"
import { Plus, Zap, Bug, ArrowUp, Calendar, Tag } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage, useTranslation } from "@/lib/i18n"

interface ChangelogSectionProps {
  // Optional props for backward compatibility
  lang?: "zh" | "en"
}

const changelogData = {
  zh: {
    title: "产品更新日志",
    subtitle: "了解我们最新的功能更新和改进",
    updates: [
      {
        version: "v0.3.0",
        date: "2025-07-23",
        type: "major",
        title: "官网上线",
        description: "全部页面接入国际化，通过语言切换可以实现中英切换。",
        changes: [
          { type: "new", text: "国际化" },
        ],
      },
    ],
  },
  en: {
    title: "Product Changelog",
    subtitle: "Stay updated with our latest features and improvements",
    updates: [
      {
        version: "v0.3.0",
        date: "2025-07-23",
        type: "major",
        title: "Official Website Launch",
        description: "All pages have been internationalized, enabling Chinese/English switching through language toggle.",
        changes: [
          { type: "new", text: "Internationalization" },
        ],
      },
    ],
  },
}

const getTypeIcon = (type: string) => {
  switch (type) {
    case "new":
      return Plus
    case "improvement":
      return ArrowUp
    case "fix":
      return Bug
    default:
      return Zap
  }
}

const getTypeColor = (type: string) => {
  switch (type) {
    case "new":
      return "bg-green-100 text-green-800"
    case "improvement":
      return "bg-blue-100 text-blue-800"
    case "fix":
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getVersionBadgeColor = (type: string) => {
  switch (type) {
    case "major":
      return "bg-purple-500"
    case "minor":
      return "bg-blue-500"
    case "patch":
      return "bg-green-500"
    default:
      return "bg-gray-500"
  }
}

export default function ChangelogSection({ lang: propLang }: ChangelogSectionProps) {
  const { language } = useLanguage()
  const lang = propLang || language
  const data = changelogData[lang]

  return (
    <section id="changelog" className="py-16 bg-white">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">{data.title}</h2>
          <p className="mt-4 text-lg text-gray-600">{data.subtitle}</p>
        </motion.div>

        <div className="space-y-8">
          {data.updates.map((update, index) => (
            <motion.div
              key={update.version}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100">
                  <div className="flex items-center justify-between flex-wrap gap-4">
                    <div className="flex items-center space-x-3">
                      <Badge className={`${getVersionBadgeColor(update.type)} text-white`}>
                        <Tag className="w-3 h-3 mr-1" />
                        {update.version}
                      </Badge>
                      <div className="flex items-center text-gray-500 text-sm">
                        <Calendar className="w-4 h-4 mr-1" />
                        {update.date}
                      </div>
                    </div>
                  </div>
                  <CardTitle className="text-xl mt-2">{update.title}</CardTitle>
                  <CardDescription className="text-gray-600">{update.description}</CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-3">
                    {update.changes.map((change, changeIndex) => {
                      const IconComponent = getTypeIcon(change.type)
                      return (
                        <motion.div
                          key={changeIndex}
                          initial={{ opacity: 0, x: -10 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: changeIndex * 0.05 }}
                          className="flex items-start space-x-3"
                        >
                          <div className={`p-1 rounded-full ${getTypeColor(change.type)}`}>
                            <IconComponent className="w-3 h-3" />
                          </div>
                          <span className="text-gray-700 text-sm leading-relaxed">{change.text}</span>
                        </motion.div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <div className="inline-flex items-center px-4 py-2 bg-gray-100 rounded-full text-gray-600 text-sm">
            <Zap className="w-4 h-4 mr-2" />
            {lang === "zh" ? "关注我们的更新，获取最新功能通知" : "Follow our updates to get notified of new features"}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
